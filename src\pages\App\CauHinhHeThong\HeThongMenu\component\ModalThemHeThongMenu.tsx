import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";

import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {useHeThongMenuContext} from "../index.context";

import {APP_NAME, ChiTietMenuProps, FormChiTietHeThongMenu, IMenuSelected, IModalThemHeThongMenuRef, IModalTimMenuChaRef} from "./index.configs";

import {NHOM_MENU, TRANG_THAI_MENU} from "../index.configs";

import "../index.default.scss";
import {ModalTimMenuCha} from "./ModalTimMenuCha";

const {ma, ten, stt, trang_thai, trang_thai_ten, ma_cha, nhom, url, app_name} = FormChiTietHeThongMenu;
//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref

const ModalThemHeThongMenuComponent = forwardRef<IModalThemHeThongMenuRef, ChiTietMenuProps>(({listMenu}: ChiTietMenuProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataMenu?: CommonExecute.Execute.IHeThongMenu) => {
      setIsOpen(true);
      console.log("dataMenu", dataMenu);
      if (dataMenu) setchitietMenu(dataMenu);
    },

    close: () => setIsOpen(false),
  }));

  const [chitietMenu, setchitietMenu] = useState<CommonExecute.Execute.IHeThongMenu | null>(null);
  const refModelTimMenuCha = useRef<IModalTimMenuChaRef>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {loading, onUpdateHeThongMenu, filterParams, setFilterParams} = useHeThongMenuContext();

  const [formThemMenuCha] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], formThemMenuCha);

  const [MenuSelected, setMenuSelected] = useState<IMenuSelected | null>(null);

  // init form data
  useEffect(() => {
    if (chitietMenu) {
      // console.log("chitietMenu", chitietMenu);
      const arrFormData = [];
      for (const key in chitietMenu) {
        arrFormData.push({
          name: key,
          value: chitietMenu[key as keyof CommonExecute.Execute.IHeThongMenu],
        });
      }
      const menuCha = listMenu.find(menu => menu.ma === chitietMenu.ma_cha);
      if (menuCha) {
        setMenuSelected({
          ma: menuCha.ma,
          ten: menuCha.ten,
        });
      } else {
        setMenuSelected(null);
      }

      formThemMenuCha.setFields(arrFormData);
    }
  }, [chitietMenu]);

  //xử lý validate form
  useEffect(() => {
    formThemMenuCha
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [formThemMenuCha, formValues]);
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setchitietMenu(null);
    formThemMenuCha.resetFields();
    setFilterParams({...filterParams});
    console.log("filterparamss", filterParams);
  }, [filterParams]);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatHeThongMenuParams = formThemMenuCha.getFieldsValue(); //lấy ra values của form
      console.log("values", values);
      //với trường hợp tạo mới -> check mã đối tác đã tồn tại
      // if (!chitietMenu) {
      //   for (let i = 0; i < listMenu.length; i++) {
      //     if (listMenu[i].ma === values.ma) {
      //       formThemMenuCha.setFields([
      //         {
      //           name: "ma",
      //           errors: ["Mã menu đã tổn tại!"],
      //         },
      //       ]);
      //       return;
      //     }
      //   }
      // }

      const response = await onUpdateHeThongMenu(values);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        closeModal();
      } else {
        console.log("cập nhật thất bại");
      }
      //cập nhật lại danh mục menu
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };

  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={formThemMenuCha} layout="vertical" initialValues={{trang_thai: TRANG_THAI_MENU[0].ma}}>
      {/* Row 1: Đội, Mã menu, Tên nguồn lực chính đại diện */}
      <Row gutter={16}>
        {/* {renderFormColum({...ma_doi_tac_ql, options: doiTacOptions, disabled: chitietMenu ? true : false})} */}

        {renderFormInputColum({...ma})}
        {renderFormInputColum({...ten})}
        {renderFormInputColum({
          ...ma_cha,
          options: [{ma: MenuSelected?.ma, ten: MenuSelected?.ten}], // Chỉ cần mảng chứa watchDLCha nếu có
          open: false,
          dropdownStyle: {display: "none"},
          labelInValue: true,
          onClick: () => refModelTimMenuCha.current?.open(),
        })}
      </Row>

      <Row gutter={16}>
        {renderFormInputColum({...nhom, options: NHOM_MENU}, 5)}
        {renderFormInputColum({...app_name, options: APP_NAME})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI_MENU}, 6)}
        {renderFormInputColum({...stt}, 5)}
      </Row>
      <Row>{renderFormInputColum({...url}, 24)}</Row>
    </Form>
  );

  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        maskClosable={false}
        className="modal-them-menu"
        title={
          <HeaderModal
            title={chitietMenu ? `Chi tiết menu ${chitietMenu.ten}` : "Tạo mới menu"}
            trang_thai_ten={chitietMenu?.trang_thai_ten || chitietMenu?.trang_thai === "D" ? "Đang sử dụng" : chitietMenu?.trang_thai === "K" ? "Ngừng sử dụng" : ""}
            trang_thai={chitietMenu?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        width={{
          xs: "50%",
          sm: "50%",
          md: "50%",
          lg: "50%",
          xl: "50%",
          xxl: "50%",
        }}
        footer={renderFooter}>
        {/* {chitietMenu?.trang_thai_ten && (
          <Tag color={getStatusColor(chitietMenu?.trang_thai)} className="mb-1.5">
            {chitietMenu.trang_thai_ten}
          </Tag>
        )} */}
        {renderForm()}
      </Modal>
      <ModalTimMenuCha
        ref={refModelTimMenuCha}
        onSelectMenuCha={MenuCha => {
          console.log("MenuCha from ModalTimMenuCha", MenuCha);
          if (MenuCha) {
            formThemMenuCha.setFieldValue("ma_cha", MenuCha.ma);
            setMenuSelected(MenuCha);
          } else {
            formThemMenuCha.setFieldValue("ma_cha", null);
          }
        }}
      />
    </Flex>
  );
});

ModalThemHeThongMenuComponent.displayName = "ModalThemHeThongMenuComponent";
export const ModalThemHeThongMenu = memo(ModalThemHeThongMenuComponent, isEqual);
