import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiLoaiHoGiaDinh, TRANG_THAI_TAO_MOI_LOAI_HO_GIA_DINH} from "../index.configs";
import {useQuanLyLoaiHoGiaDinhContext} from "../index.context";
import {ChiTietLoaiHoGiaDinhProps, IModalChiTietLoaiHoGiaDinhRef} from "./Constant";
const {ma, ten, stt, trang_thai} = FormTaoMoiLoaiHoGiaDinh;

const ModalChiTietLoaiHoGiaDinhComponent = forwardRef<IModalChiTietLoaiHoGiaDinhRef, ChiTietLoaiHoGiaDinhProps>(({listLoaiHoGiaDinh}: ChiTietLoaiHoGiaDinhProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataLoaiHoGiaDinh?: CommonExecute.Execute.ILoaiHoGiaDinh) => {
      setIsOpen(true);
      if (dataLoaiHoGiaDinh) setChiTietLoaiHoGiaDinh(dataLoaiHoGiaDinh); // nếu có dữ liệu -> set chi tiết LoaiHoGiaDinh -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietLoaiHoGiaDinh, setChiTietLoaiHoGiaDinh] = useState<CommonExecute.Execute.ILoaiHoGiaDinh | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietLoaiHoGiaDinh, getListLoaiHoGiaDinh, loading} = useQuanLyLoaiHoGiaDinhContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietLoaiHoGiaDinh) {
      const arrFormData = [];
      for (const key in chiTietLoaiHoGiaDinh) {
        arrFormData.push({
          name: key,
          value: chiTietLoaiHoGiaDinh[key as keyof CommonExecute.Execute.ILoaiHoGiaDinh],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietLoaiHoGiaDinh]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietLoaiHoGiaDinh(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatLoaiHoGiaDinhParams = form.getFieldsValue(); //lấy ra values của form

      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietLoaiHoGiaDinh;

      await capNhatChiTietLoaiHoGiaDinh(values, isEditMode); //cập nhật lại LoaiHoGiaDinh
      await getListLoaiHoGiaDinh(); //lấy lại danh sách LoaiHoGiaDinh
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span: number = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietLoaiHoGiaDinh ? true : false}, 6)}
        {renderFormColum({...ten}, 18)}
      </Row>

      <Row gutter={16}>
        {renderFormColum({...stt}, 6)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_LOAI_HO_GIA_DINH}, 18)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietLoaiHoGiaDinh ? `${chiTietLoaiHoGiaDinh.ten}` : "Tạo mới loại hộ gia đình bhxh"} trang_thai_ten={chiTietLoaiHoGiaDinh?.trang_thai_ten} trang_thai={chiTietLoaiHoGiaDinh?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={600}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietLoaiHoGiaDinhComponent.displayName = "ModalChiTietLoaiHoGiaDinhComponent";
export const ModalChiTietLoaiHoGiaDinh = memo(ModalChiTietLoaiHoGiaDinhComponent, isEqual);
