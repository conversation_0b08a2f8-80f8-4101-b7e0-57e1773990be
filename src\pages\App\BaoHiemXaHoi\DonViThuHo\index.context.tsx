import {createContext, useContext} from "react";

import {IQuanLyDonViThuHoContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyDonViThuHoContext = createContext<IQuanLyDonViThuHoContextProps>({
  listDonViThuHo: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListDonViThuHo: async () => Promise.resolve(),
  getChiTietDonViThuHo: async () => Promise.resolve({} as CommonExecute.Execute.IDonViThuHo),
  capNhatChiTietDonViThuHo: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyDonViThuHoContext = () => useContext(QuanLyDonViThuHoContext);