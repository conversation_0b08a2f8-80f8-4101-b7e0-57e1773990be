import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiDonViThuHo, TRANG_THAI_TAO_MOI_DON_VI_THU_HO} from "../index.configs";
import {useQuanLyDonViThuHoContext} from "../index.context";
import {ChiTietDonViThuHoProps, IModalChiTietDonViThuHoRef} from "./Constant";
const {ma, ten, ten_tat, ten_e, dchi, dthoai, mst, stt, trang_thai} = FormTaoMoiDonViThuHo;

const ModalChiTietDonViThuHoComponent = forwardRef<IModalChiTietDonViThuHoRef, ChiTietDonViThuHoProps>(({listDonViThuHo}: ChiTietDonViThuHoProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDonViThuHo?: CommonExecute.Execute.IDonViThuHo) => {
      setIsOpen(true);
      if (dataDonViThuHo) setChiTietDonViThuHo(dataDonViThuHo); // nếu có dữ liệu -> set chi tiết DonViThuHo -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDonViThuHo, setChiTietDonViThuHo] = useState<CommonExecute.Execute.IDonViThuHo | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDonViThuHo, getListDonViThuHo, loading} = useQuanLyDonViThuHoContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDonViThuHo) {
      const arrFormData = [];
      for (const key in chiTietDonViThuHo) {
        arrFormData.push({
          name: key,
          value: chiTietDonViThuHo[key as keyof CommonExecute.Execute.IDonViThuHo],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDonViThuHo]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDonViThuHo(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDonViThuHoParams = form.getFieldsValue(); //lấy ra values của form

      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietDonViThuHo;

      await capNhatChiTietDonViThuHo(values, isEditMode); //cập nhật lại DonViThuHo
      await getListDonViThuHo(); //lấy lại danh sách DonViThuHo
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span: number = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDonViThuHo ? true : false}, 10)}
        {renderFormColum({...ten}, 14)}
       
      </Row>
      <Row gutter={16}>
        {renderFormColum({...ten_tat}, 10)}
        {renderFormColum({...ten_e}, 14)}
       
      </Row>
       <Row gutter={16}>
        {renderFormColum({...dchi}, 16)}
        {renderFormColum({...dthoai}, 8)}
      </Row>
      <Row gutter={16}>
        {renderFormColum({...mst}, 10)}
        {renderFormColum({...stt}, 6)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DON_VI_THU_HO}, 8)}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietDonViThuHo ? `${chiTietDonViThuHo.ten}` : "Tạo mới đơn vị thu hộ bhxh"} trang_thai_ten={chiTietDonViThuHo?.trang_thai_ten} trang_thai={chiTietDonViThuHo?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={800}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDonViThuHoComponent.displayName = "ModalChiTietDonViThuHoComponent";
export const ModalChiTietDonViThuHo = memo(ModalChiTietDonViThuHoComponent, isEqual);
