import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {FormTaoMoiDanhMucMaBenhICD, TRANG_THAI_TAO_MOI_DANH_MUC_MA_BENH_ICD} from "../index.configs";
import {useQuanLyDanhMucMaBenhICDContext} from "../index.context";
import {ChiTietDanhMucMaBenhICDProps, IModalChiTietDanhMucMaBenhICDRef} from "./Constant";
const {ma, ten, ten_e, ma_ct, ma_byt,stt,trang_thai} = FormTaoMoiDanhMucMaBenhICD;

const ModalChiTietDanhMucMaBenhICDComponent = forwardRef<IModalChiTietDanhMucMaBenhICDRef, ChiTietDanhMucMaBenhICDProps>(({listDanhMucMaBenhICD}: ChiTietDanhMucMaBenhICDProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucMaBenhICD?: CommonExecute.Execute.IDanhMucMaBenhICD) => {
      setIsOpen(true);
      if (dataDanhMucMaBenhICD) setChiTietDanhMucMaBenhICD(dataDanhMucMaBenhICD); // nếu có dữ liệu -> set chi tiết DanhMucMaBenhICD -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDanhMucMaBenhICD, setChiTietDanhMucMaBenhICD] = useState<CommonExecute.Execute.IDanhMucMaBenhICD | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDanhMucMaBenhICD, getListDanhMucMaBenhICD, loading} = useQuanLyDanhMucMaBenhICDContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDanhMucMaBenhICD) {
      const arrFormData = [];
      for (const key in chiTietDanhMucMaBenhICD) {
        arrFormData.push({
          name: key,
          value: chiTietDanhMucMaBenhICD[key as keyof CommonExecute.Execute.IDanhMucMaBenhICD],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDanhMucMaBenhICD]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDanhMucMaBenhICD(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDanhMucMaBenhICDParams = form.getFieldsValue(); //lấy ra values của form

      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietDanhMucMaBenhICD;


      await capNhatChiTietDanhMucMaBenhICD(values, isEditMode); //cập nhật lại DanhMucMaBenhICD
      await getListDanhMucMaBenhICD(); //lấy lại danh sách DanhMucMaBenhICD
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span: number = 12) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDanhMucMaBenhICD ? true : false}, 8)}
        {renderFormColum({...ten}, 8)}
        {renderFormColum({...ten_e}, 8)}

      </Row>

      <Row gutter={16}>
        {renderFormColum({...ma_ct}, 8)}
        {renderFormColum({...ma_byt}, 8)}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DANH_MUC_MA_BENH_ICD}, 8)}

      </Row>
      
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietDanhMucMaBenhICD ? `${chiTietDanhMucMaBenhICD.ten}` : "Tạo mới danh mục Nhóm mã bệnh"} trang_thai_ten={chiTietDanhMucMaBenhICD?.trang_thai_ten} trang_thai={chiTietDanhMucMaBenhICD?.trang_thai} />}
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={600}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietDanhMucMaBenhICDComponent.displayName = "ModalChiTietDanhMucMaBenhICDComponent";
export const ModalChiTietDanhMucMaBenhICD = memo(ModalChiTietDanhMucMaBenhICDComponent, isEqual);
